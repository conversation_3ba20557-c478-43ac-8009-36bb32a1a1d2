/* ===== THEME SYSTEM - LIGHT & DARK MODES ===== */
/* Centralized theme definitions for consistent theming */

/* ===== LIGHT THEME ===== */
.layout-theme-light {
    /* ===== SURFACE COLORS ===== */
    --surface-ground: var(--gray-50);
    --surface-card: #ffffff;
    --surface-overlay: #ffffff;
    --surface-border: var(--gray-200);
    --surface-hover: var(--gray-100);
    --surface-pressed: var(--gray-200);
    --surface-disabled: var(--gray-100);

    /* Enhanced Surface Colors */
    --surface-card-enhanced: #ffffff;
    --surface-hover-enhanced: var(--gray-50);
    --border-color-enhanced: var(--gray-200);
    --background-enhanced: var(--gray-50);

    /* ===== TEXT COLORS ===== */
    --text-color: var(--gray-700);
    --text-color-secondary: var(--gray-500);
    --text-color-disabled: var(--gray-400);
    --text-color-inverse: #ffffff;

    /* Enhanced Text Colors */
    --text-primary-enhanced: var(--gray-800);
    --text-secondary-enhanced: var(--gray-600);

    /* ===== PRIMARY COLORS ===== */
    --primary-color: var(--brand-primary);
    --primary-color-text: #ffffff;
    --primary-50: var(--brand-lighter);
    --primary-100: var(--brand-light);
    --primary-200: #fecaca;
    --primary-300: #fca5a5;
    --primary-400: #f87171;
    --primary-500: var(--brand-primary);
    --primary-600: var(--brand-secondary);
    --primary-700: var(--brand-tertiary);
    --primary-800: #7f1d1d;
    --primary-900: #450a0a;

    /* ===== COMPONENT SPECIFIC COLORS ===== */
    /* Focus States */
    --focus-ring: 0 0 0 3px rgba(220, 38, 38, 0.2);
    --focus-ring-color: rgba(220, 38, 38, 0.2);

    /* Link Colors */
    --link-color: var(--brand-primary);
    --link-hover-color: var(--brand-secondary);

    /* ===== SHADOWS ===== */
    --shadow-color: rgba(0, 0, 0, 0.1);
    --shadow-brand-color: rgba(220, 38, 38, 0.15);

    /* Enhanced Shadow System */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* ===== DARK THEME ===== */
.layout-theme-dark {
    /* ===== SURFACE COLORS ===== */
    --surface-ground: var(--gray-900);
    --surface-card: var(--gray-800);
    --surface-overlay: var(--gray-800);
    --surface-border: var(--gray-700);
    --surface-hover: var(--gray-700);
    --surface-pressed: var(--gray-600);
    --surface-disabled: var(--gray-800);

    /* Enhanced Surface Colors */
    --surface-card-enhanced: var(--gray-800);
    --surface-hover-enhanced: var(--gray-700);
    --border-color-enhanced: var(--gray-600);
    --background-enhanced: var(--gray-900);

    /* ===== TEXT COLORS ===== */
    --text-color: rgba(255, 255, 255, 0.87);
    --text-color-secondary: rgba(255, 255, 255, 0.6);
    --text-color-disabled: rgba(255, 255, 255, 0.38);
    --text-color-inverse: var(--gray-900);

    /* Enhanced Text Colors */
    --text-primary-enhanced: rgba(255, 255, 255, 0.9);
    --text-secondary-enhanced: rgba(255, 255, 255, 0.7);

    /* ===== PRIMARY COLORS ===== */
    --primary-color: var(--brand-primary);
    --primary-color-text: #ffffff;
    --primary-50: #450a0a;
    --primary-100: #7f1d1d;
    --primary-200: var(--brand-tertiary);
    --primary-300: var(--brand-secondary);
    --primary-400: var(--brand-primary);
    --primary-500: #f87171;
    --primary-600: #fca5a5;
    --primary-700: #fecaca;
    --primary-800: var(--brand-light);
    --primary-900: var(--brand-lighter);

    /* ===== COMPONENT SPECIFIC COLORS ===== */
    /* Focus States */
    --focus-ring: 0 0 0 3px rgba(220, 38, 38, 0.3);
    --focus-ring-color: rgba(220, 38, 38, 0.3);

    /* Link Colors */
    --link-color: var(--brand-accent);
    --link-hover-color: var(--brand-primary);

    /* ===== SHADOWS ===== */
    --shadow-color: rgba(0, 0, 0, 0.3);
    --shadow-brand-color: rgba(220, 38, 38, 0.2);

    /* Enhanced Shadow System */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.2);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
}

/* ===== THEME TRANSITION ===== */
* {
    transition: background-color var(--transition-normal), 
                border-color var(--transition-normal), 
                color var(--transition-normal);
}

/* ===== THEME-AWARE UTILITIES ===== */
.theme-transition {
    transition: background-color var(--transition-normal), 
                border-color var(--transition-normal), 
                color var(--transition-normal),
                box-shadow var(--transition-normal);
}

/* ===== COMPONENT THEME OVERRIDES ===== */
/* These ensure consistent theming across all components */

/* Card theming */
.card {
    background: var(--surface-card-enhanced);
    border: 1px solid var(--border-color-enhanced);
    color: var(--text-color);
}

/* Button theming */
.btn-primary {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--primary-color-text);
}

/* Input theming */
.form-control {
    background: var(--surface-card);
    border-color: var(--surface-border);
    color: var(--text-color);
}

/* Table theming */
.table {
    background: var(--surface-card);
    color: var(--text-color);
}

.table th {
    background: var(--surface-hover);
    border-color: var(--surface-border);
}

.table td {
    border-color: var(--surface-border);
}

/* Modal theming */
.modal-content {
    background: var(--surface-card);
    border-color: var(--surface-border);
}

/* Dropdown theming */
.dropdown-menu {
    background: var(--surface-overlay);
    border-color: var(--surface-border);
}

.dropdown-item {
    color: var(--text-color);
}

.dropdown-item:hover {
    background: var(--surface-hover);
}

/* ===== ACCESSIBILITY ENHANCEMENTS ===== */
@media (prefers-reduced-motion: reduce) {
    * {
        transition: none !important;
        animation: none !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --surface-border: var(--gray-900);
        --text-color: var(--gray-900);
    }
    
    .layout-theme-dark {
        --surface-border: #ffffff;
        --text-color: #ffffff;
    }
}
