# CSS Architecture - EXEX Debt Management System

## Overview
This document outlines the refactored CSS architecture for the EXEX Debt Management System, designed for maintainability, scalability, and consistency.

## Architecture Structure

```
src/assets/styles/
├── design-tokens/
│   ├── _tokens.scss      # Design system tokens (colors, spacing, typography)
│   └── _themes.scss      # Light/dark theme definitions
├── mixins/
│   └── _mixins.scss      # Reusable SCSS mixins
├── utilities/
│   └── _utilities.scss   # Atomic utility classes
└── README.md            # This documentation
```

## Design Tokens

### Colors
- **Brand Colors**: `--brand-primary`, `--brand-secondary`, `--brand-tertiary`
- **Semantic Colors**: `--color-success`, `--color-warning`, `--color-error`, `--color-info`
- **Neutral Colors**: `--gray-50` to `--gray-900`

### Spacing System
- **Base Unit**: 0.25rem (4px)
- **Scale**: `--space-1` (4px) to `--space-24` (96px)
- **Semantic**: `--spacing-xs`, `--spacing-sm`, `--spacing-md`, `--spacing-lg`, `--spacing-xl`

### Typography
- **Font Sizes**: `--text-xs` to `--text-5xl`
- **Font Weights**: `--font-weight-light` to `--font-weight-extrabold`
- **Line Heights**: `--leading-none` to `--leading-loose`

### Border Radius
- **Scale**: `--radius-none` to `--radius-2xl`
- **Special**: `--radius-full` for circular elements

### Shadows
- **Standard**: `--shadow-sm` to `--shadow-2xl`
- **Brand**: `--shadow-brand`, `--shadow-brand-lg`

## Theme System

### Light Theme (`.layout-theme-light`)
- Clean, bright interface with subtle shadows
- High contrast for accessibility
- Brand colors with appropriate opacity

### Dark Theme (`.layout-theme-dark`)
- Dark surfaces with enhanced shadows
- Adjusted text colors for readability
- Brand colors adapted for dark backgrounds

### Theme Switching
Themes are applied via CSS classes and use CSS custom properties for seamless transitions.

## Mixins

### Layout Mixins
- `@include flex-center` - Center content with flexbox
- `@include flex-between` - Space-between layout
- `@include absolute-center` - Absolute positioning center

### Visual Effect Mixins
- `@include card-shadow($level)` - Consistent card shadows
- `@include hover-lift($distance)` - Hover elevation effect
- `@include focus-ring($color)` - Accessible focus states

### Button Mixins
- `@include button-primary` - Primary button styling
- `@include button-secondary` - Secondary button styling
- `@include button-ghost` - Ghost button styling

### Form Mixins
- `@include input-base` - Base input styling
- `@include form-group` - Form group layout

### Responsive Mixins
- `@include mobile-only` - Mobile-specific styles
- `@include tablet-up` - Tablet and up
- `@include desktop-up` - Desktop and up

## Utility Classes

### Spacing
- Margin: `.m-0` to `.m-8`, `.mt-*`, `.mb-*`, etc.
- Padding: `.p-0` to `.p-8`

### Colors
- Text: `.text-brand`, `.text-success`, `.text-error`
- Background: `.bg-brand`, `.bg-success`, `.bg-error`
- Border: `.border-brand`

### Typography
- Sizes: `.text-xs` to `.text-3xl`
- Weights: `.font-light` to `.font-bold`
- Alignment: `.text-left`, `.text-center`, `.text-right`

### Layout
- Display: `.d-flex`, `.d-block`, `.d-none`
- Flexbox: `.justify-center`, `.items-center`, `.flex-column`
- Position: `.position-relative`, `.position-absolute`

### Visual
- Shadows: `.shadow-sm` to `.shadow-xl`, `.shadow-brand`
- Border Radius: `.rounded-sm` to `.rounded-xl`
- Transitions: `.transition-all`, `.transition-fast`

## Best Practices

### 1. Use Design Tokens
```scss
// ✅ Good
.my-component {
    padding: var(--spacing-md);
    color: var(--brand-primary);
    border-radius: var(--radius-lg);
}

// ❌ Avoid
.my-component {
    padding: 16px;
    color: #dc2626;
    border-radius: 12px;
}
```

### 2. Leverage Mixins
```scss
// ✅ Good
.my-button {
    @include button-primary;
}

// ❌ Avoid
.my-button {
    background: linear-gradient(135deg, var(--brand-primary), var(--brand-secondary));
    color: white;
    padding: var(--space-3) var(--space-4);
    // ... many more properties
}
```

### 3. Use Utility Classes for Rapid Development
```html
<!-- ✅ Good for quick layouts -->
<div class="d-flex items-center gap-4 p-4 bg-brand text-white rounded-lg">
    Content
</div>
```

### 4. Responsive Design
```scss
// ✅ Good
.my-component {
    @include mobile-only {
        font-size: var(--text-sm);
    }
    
    @include desktop-up {
        font-size: var(--text-lg);
    }
}
```

### 5. Theme-Aware Styling
```scss
// ✅ Good - automatically adapts to theme
.my-component {
    background: var(--surface-card);
    color: var(--text-color);
    border: 1px solid var(--surface-border);
}
```

## Migration Guide

### From Old System
1. Replace hardcoded values with design tokens
2. Use new mixins instead of repetitive CSS
3. Leverage utility classes for common patterns
4. Update responsive breakpoints to use new mixins

### Component Updates
- Replace `var(--nisshin-red-primary)` with `var(--brand-primary)`
- Replace `var(--spacing-*)` with standardized spacing tokens
- Use new shadow and border-radius tokens

## Performance Considerations

1. **CSS Custom Properties**: Enable dynamic theming without CSS regeneration
2. **Utility Classes**: Reduce CSS bundle size through reuse
3. **Mixins**: Generate optimized CSS at build time
4. **Design Tokens**: Centralized values reduce redundancy

## Browser Support

- Modern browsers with CSS Custom Properties support
- Fallbacks provided for critical properties
- Progressive enhancement for advanced features

## Maintenance

### Adding New Tokens
1. Add to `_tokens.scss`
2. Update theme files if needed
3. Document in this README
4. Create utility classes if appropriate

### Creating New Mixins
1. Add to `_mixins.scss`
2. Follow naming conventions
3. Include parameters for flexibility
4. Document usage examples

### Updating Themes
1. Modify `_themes.scss`
2. Test in both light and dark modes
3. Ensure accessibility compliance
4. Update component overrides if needed
