/* ===== LAYOUT MIXINS ===== */
/* Legacy mixins for backward compatibility */
/* Use mixins from assets/styles/mixins/_mixins.scss for new development */

@mixin focused() {
    outline: 0 none;
    outline-offset: 0;
    transition: box-shadow var(--transition-normal);
    box-shadow: var(--focus-ring);
}

@mixin focused-inset() {
    outline: 0 none;
    outline-offset: 0;
    transition: box-shadow var(--transition-normal);
    box-shadow: inset var(--focus-ring);
}
