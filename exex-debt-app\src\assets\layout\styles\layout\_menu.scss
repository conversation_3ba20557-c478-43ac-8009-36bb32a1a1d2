.layout-sidebar {
    position: fixed;
    width: 200px;
    height: calc(100vh - 9rem);
    z-index: 999;
    overflow-y: auto;
    user-select: none;
    top: 7rem;
    left: 2rem;
    transition: transform $transitionDuration, left $transitionDuration, box-shadow 0.2s ease, background-color 0.3s ease;
    background-color: var(--surface-card);
    border: 1px solid var(--surface-border);
    border-radius: var(--radius-xl, 16px);
    padding: var(--spacing-lg, 1.5rem);
    box-shadow: 0 10px 25px rgba(220, 38, 38, 0.08), 0 4px 12px rgba(0, 0, 0, 0.05);

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg,
            var(--nisshin-red-primary, #dc2626) 0%,
            var(--nisshin-red-accent, #f87171) 50%,
            var(--nisshin-red-primary, #dc2626) 100%);
        border-radius: var(--radius-xl, 16px) var(--radius-xl, 16px) 0 0;
    }
}

.layout-menu {
    margin: 0;
    padding: 0;
    list-style-type: none;

    .layout-root-menuitem {
        >.layout-menuitem-root-text {
            font-size: 0.75rem;
            text-transform: uppercase;
            font-weight: 700;
            color: var(--nisshin-red-primary, #dc2626);
            margin: var(--spacing-lg, 1.5rem) 0 var(--spacing-sm, 0.5rem) 0;
            letter-spacing: 0.05em;
            padding: var(--spacing-xs, 0.25rem) var(--spacing-sm, 0.5rem);
            background: rgba(220, 38, 38, 0.1);
            border-radius: var(--radius-md, 8px);
            border-left: 3px solid var(--nisshin-red-primary, #dc2626);
        }

        >a {
            display: none;
        }
    }

    a {
        user-select: none;

        &.active-menuitem {
            >.layout-submenu-toggler {
                transform: rotate(-180deg);
            }
        }
    }

    li.active-menuitem {
        >a {
            .layout-submenu-toggler {
                transform: rotate(-180deg);
            }
        }
    }

    ul {
        margin: 0;
        padding: 0;
        list-style-type: none;

        a {
            display: flex;
            align-items: center;
            position: relative;
            outline: 0 none;
            color: var(--text-color);
            cursor: pointer;
            padding: var(--spacing-md, 1rem);
            border-radius: var(--radius-md, 8px);
            transition: all 0.2s ease;
            margin-bottom: var(--spacing-xs, 0.25rem);
            border: 1px solid transparent;
            font-weight: 500;

            .layout-menuitem-icon {
                margin-right: var(--spacing-sm, 0.5rem);
                color: var(--nisshin-red-primary, #dc2626);
                font-size: 1.1rem;
                transition: all 0.2s ease;
            }

            .layout-submenu-toggler {
                font-size: 75%;
                margin-left: auto;
                transition: transform $transitionDuration;
                color: var(--nisshin-red-primary, #dc2626);
            }

            &.active-route {
                font-weight: 700;
                color: white;
                background: linear-gradient(135deg, var(--nisshin-red-primary, #dc2626), var(--nisshin-red-secondary, #b91c1c));
                border-color: var(--nisshin-red-primary, #dc2626);
                box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
                transform: translateX(2px);

                .layout-menuitem-icon {
                    color: white;
                    transform: scale(1.1);
                }
            }

            &:hover {
                color: var(--nisshin-red-primary, #dc2626);
                background: var(--surface-hover);
                border-color: var(--nisshin-red-primary, #dc2626);
                transform: translateX(2px);
                box-shadow: 0 2px 8px rgba(220, 38, 38, 0.15);

                .layout-menuitem-icon {
                    transform: scale(1.1);
                    color: var(--nisshin-red-secondary, #b91c1c);
                }
            }

            &:focus {
                outline: none;
                box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.2);
            }
        }

        ul {
            overflow: hidden;
            border-radius: $borderRadius;

            li {
                a {
                    margin-left: 1rem;
                }

                li {
                    a {
                        margin-left: 2rem;
                    }

                    li {
                        a {
                            margin-left: 2.5rem;
                        }

                        li {
                            a {
                                margin-left: 3rem;
                            }

                            li {
                                a {
                                    margin-left: 3.5rem;
                                }

                                li {
                                    a {
                                        margin-left: 4rem;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
