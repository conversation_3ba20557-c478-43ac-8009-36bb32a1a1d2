/* ===== UTILITY CLASSES - ATOMIC CSS UTILITIES ===== */
/* Reusable utility classes for rapid development */

/* ===== SPACING UTILITIES ===== */
/* Margin utilities */
.m-0 { margin: var(--space-0) !important; }
.m-1 { margin: var(--space-1) !important; }
.m-2 { margin: var(--space-2) !important; }
.m-3 { margin: var(--space-3) !important; }
.m-4 { margin: var(--space-4) !important; }
.m-5 { margin: var(--space-5) !important; }
.m-6 { margin: var(--space-6) !important; }
.m-8 { margin: var(--space-8) !important; }

/* Margin top */
.mt-0 { margin-top: var(--space-0) !important; }
.mt-1 { margin-top: var(--space-1) !important; }
.mt-2 { margin-top: var(--space-2) !important; }
.mt-3 { margin-top: var(--space-3) !important; }
.mt-4 { margin-top: var(--space-4) !important; }
.mt-6 { margin-top: var(--space-6) !important; }
.mt-8 { margin-top: var(--space-8) !important; }

/* Margin bottom */
.mb-0 { margin-bottom: var(--space-0) !important; }
.mb-1 { margin-bottom: var(--space-1) !important; }
.mb-2 { margin-bottom: var(--space-2) !important; }
.mb-3 { margin-bottom: var(--space-3) !important; }
.mb-4 { margin-bottom: var(--space-4) !important; }
.mb-6 { margin-bottom: var(--space-6) !important; }
.mb-8 { margin-bottom: var(--space-8) !important; }

/* Padding utilities */
.p-0 { padding: var(--space-0) !important; }
.p-1 { padding: var(--space-1) !important; }
.p-2 { padding: var(--space-2) !important; }
.p-3 { padding: var(--space-3) !important; }
.p-4 { padding: var(--space-4) !important; }
.p-5 { padding: var(--space-5) !important; }
.p-6 { padding: var(--space-6) !important; }
.p-8 { padding: var(--space-8) !important; }

/* ===== COLOR UTILITIES ===== */
/* Brand colors */
.text-brand { color: var(--brand-primary) !important; }
.text-brand-secondary { color: var(--brand-secondary) !important; }
.text-brand-accent { color: var(--brand-accent) !important; }

.bg-brand { background-color: var(--brand-primary) !important; }
.bg-brand-secondary { background-color: var(--brand-secondary) !important; }
.bg-brand-light { background-color: var(--brand-light) !important; }

.border-brand { border-color: var(--brand-primary) !important; }

/* Semantic colors */
.text-success { color: var(--color-success) !important; }
.text-warning { color: var(--color-warning) !important; }
.text-error { color: var(--color-error) !important; }
.text-info { color: var(--color-info) !important; }

.bg-success { background-color: var(--color-success) !important; }
.bg-warning { background-color: var(--color-warning) !important; }
.bg-error { background-color: var(--color-error) !important; }
.bg-info { background-color: var(--color-info) !important; }

/* Neutral colors */
.text-gray-400 { color: var(--gray-400) !important; }
.text-gray-500 { color: var(--gray-500) !important; }
.text-gray-600 { color: var(--gray-600) !important; }
.text-gray-700 { color: var(--gray-700) !important; }

.bg-gray-50 { background-color: var(--gray-50) !important; }
.bg-gray-100 { background-color: var(--gray-100) !important; }
.bg-gray-200 { background-color: var(--gray-200) !important; }

/* ===== TYPOGRAPHY UTILITIES ===== */
/* Font sizes */
.text-xs { font-size: var(--text-xs) !important; }
.text-sm { font-size: var(--text-sm) !important; }
.text-base { font-size: var(--text-base) !important; }
.text-lg { font-size: var(--text-lg) !important; }
.text-xl { font-size: var(--text-xl) !important; }
.text-2xl { font-size: var(--text-2xl) !important; }
.text-3xl { font-size: var(--text-3xl) !important; }

/* Font weights */
.font-light { font-weight: var(--font-weight-light) !important; }
.font-normal { font-weight: var(--font-weight-normal) !important; }
.font-medium { font-weight: var(--font-weight-medium) !important; }
.font-semibold { font-weight: var(--font-weight-semibold) !important; }
.font-bold { font-weight: var(--font-weight-bold) !important; }

/* Text alignment */
.text-left { text-align: left !important; }
.text-center { text-align: center !important; }
.text-right { text-align: right !important; }

/* Text utilities */
.text-truncate {
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
}

.text-uppercase { text-transform: uppercase !important; }
.text-lowercase { text-transform: lowercase !important; }
.text-capitalize { text-transform: capitalize !important; }

/* ===== LAYOUT UTILITIES ===== */
/* Display */
.d-none { display: none !important; }
.d-block { display: block !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }
.d-flex { display: flex !important; }
.d-grid { display: grid !important; }

/* Flexbox utilities */
.flex-row { flex-direction: row !important; }
.flex-column { flex-direction: column !important; }
.flex-wrap { flex-wrap: wrap !important; }
.flex-nowrap { flex-wrap: nowrap !important; }

.justify-start { justify-content: flex-start !important; }
.justify-center { justify-content: center !important; }
.justify-end { justify-content: flex-end !important; }
.justify-between { justify-content: space-between !important; }
.justify-around { justify-content: space-around !important; }

.items-start { align-items: flex-start !important; }
.items-center { align-items: center !important; }
.items-end { align-items: flex-end !important; }
.items-stretch { align-items: stretch !important; }

.flex-1 { flex: 1 1 0% !important; }
.flex-auto { flex: 1 1 auto !important; }
.flex-none { flex: none !important; }

/* Gap utilities */
.gap-1 { gap: var(--space-1) !important; }
.gap-2 { gap: var(--space-2) !important; }
.gap-3 { gap: var(--space-3) !important; }
.gap-4 { gap: var(--space-4) !important; }
.gap-6 { gap: var(--space-6) !important; }

/* ===== POSITIONING UTILITIES ===== */
.position-relative { position: relative !important; }
.position-absolute { position: absolute !important; }
.position-fixed { position: fixed !important; }
.position-sticky { position: sticky !important; }

/* ===== BORDER UTILITIES ===== */
.border { border: 1px solid var(--surface-border) !important; }
.border-0 { border: 0 !important; }
.border-t { border-top: 1px solid var(--surface-border) !important; }
.border-b { border-bottom: 1px solid var(--surface-border) !important; }
.border-l { border-left: 1px solid var(--surface-border) !important; }
.border-r { border-right: 1px solid var(--surface-border) !important; }

/* Border radius */
.rounded-none { border-radius: var(--radius-none) !important; }
.rounded-sm { border-radius: var(--radius-sm) !important; }
.rounded { border-radius: var(--radius-md) !important; }
.rounded-lg { border-radius: var(--radius-lg) !important; }
.rounded-xl { border-radius: var(--radius-xl) !important; }
.rounded-full { border-radius: var(--radius-full) !important; }

/* ===== SHADOW UTILITIES ===== */
.shadow-none { box-shadow: none !important; }
.shadow-sm { box-shadow: var(--shadow-sm) !important; }
.shadow { box-shadow: var(--shadow-md) !important; }
.shadow-lg { box-shadow: var(--shadow-lg) !important; }
.shadow-xl { box-shadow: var(--shadow-xl) !important; }

.shadow-brand { box-shadow: var(--shadow-brand) !important; }
.shadow-brand-lg { box-shadow: var(--shadow-brand-lg) !important; }

/* ===== INTERACTION UTILITIES ===== */
.cursor-pointer { cursor: pointer !important; }
.cursor-not-allowed { cursor: not-allowed !important; }
.cursor-default { cursor: default !important; }

.pointer-events-none { pointer-events: none !important; }
.pointer-events-auto { pointer-events: auto !important; }

/* ===== TRANSITION UTILITIES ===== */
.transition-none { transition: none !important; }
.transition-all { transition: var(--transition-all) !important; }
.transition-fast { transition: all var(--transition-fast) !important; }
.transition-slow { transition: all var(--transition-slow) !important; }

/* ===== OPACITY UTILITIES ===== */
.opacity-0 { opacity: 0 !important; }
.opacity-25 { opacity: 0.25 !important; }
.opacity-50 { opacity: 0.5 !important; }
.opacity-75 { opacity: 0.75 !important; }
.opacity-100 { opacity: 1 !important; }

/* ===== WIDTH & HEIGHT UTILITIES ===== */
.w-auto { width: auto !important; }
.w-full { width: 100% !important; }
.w-screen { width: 100vw !important; }

.h-auto { height: auto !important; }
.h-full { height: 100% !important; }
.h-screen { height: 100vh !important; }

/* ===== OVERFLOW UTILITIES ===== */
.overflow-hidden { overflow: hidden !important; }
.overflow-auto { overflow: auto !important; }
.overflow-scroll { overflow: scroll !important; }
.overflow-visible { overflow: visible !important; }

/* ===== Z-INDEX UTILITIES ===== */
.z-0 { z-index: 0 !important; }
.z-10 { z-index: 10 !important; }
.z-20 { z-index: 20 !important; }
.z-30 { z-index: 30 !important; }
.z-40 { z-index: 40 !important; }
.z-50 { z-index: 50 !important; }

/* ===== RESPONSIVE UTILITIES ===== */
@include tablet-up {
    .md\:d-block { display: block !important; }
    .md\:d-flex { display: flex !important; }
    .md\:d-none { display: none !important; }
    
    .md\:text-left { text-align: left !important; }
    .md\:text-center { text-align: center !important; }
    .md\:text-right { text-align: right !important; }
}

@include desktop-up {
    .lg\:d-block { display: block !important; }
    .lg\:d-flex { display: flex !important; }
    .lg\:d-none { display: none !important; }
    
    .lg\:text-left { text-align: left !important; }
    .lg\:text-center { text-align: center !important; }
    .lg\:text-right { text-align: right !important; }
}
