/* ===== DESIGN TOKENS - EXEX DEBT MANAGEMENT SYSTEM ===== */
/* Centralized design tokens for consistent styling across the application */

/* ===== BRAND COLORS - NISSHIN VIETNAM ===== */
:root {
    /* Primary Brand Colors */
    --brand-primary: #dc2626;
    --brand-secondary: #b91c1c;
    --brand-tertiary: #991b1b;
    --brand-accent: #f87171;
    --brand-light: #fef2f2;
    --brand-lighter: #fef7f7;

    /* ===== SEMANTIC COLORS ===== */
    /* Success Colors */
    --color-success: #10b981;
    --color-success-dark: #059669;
    --color-success-light: #d1fae5;

    /* Warning Colors */
    --color-warning: #f59e0b;
    --color-warning-dark: #d97706;
    --color-warning-light: #fef3c7;

    /* Error Colors */
    --color-error: var(--brand-primary);
    --color-error-dark: var(--brand-secondary);
    --color-error-light: var(--brand-light);

    /* Info Colors */
    --color-info: #3b82f6;
    --color-info-dark: #2563eb;
    --color-info-light: #dbeafe;

    /* ===== NEUTRAL COLORS ===== */
    /* Gray Scale */
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;

    /* ===== SPACING SYSTEM ===== */
    /* Base spacing unit: 0.25rem (4px) */
    --space-0: 0;
    --space-1: 0.25rem;   /* 4px */
    --space-2: 0.5rem;    /* 8px */
    --space-3: 0.75rem;   /* 12px */
    --space-4: 1rem;      /* 16px */
    --space-5: 1.25rem;   /* 20px */
    --space-6: 1.5rem;    /* 24px */
    --space-8: 2rem;      /* 32px */
    --space-10: 2.5rem;   /* 40px */
    --space-12: 3rem;     /* 48px */
    --space-16: 4rem;     /* 64px */
    --space-20: 5rem;     /* 80px */
    --space-24: 6rem;     /* 96px */

    /* Semantic Spacing */
    --spacing-xs: var(--space-1);
    --spacing-sm: var(--space-2);
    --spacing-md: var(--space-4);
    --spacing-lg: var(--space-6);
    --spacing-xl: var(--space-8);
    --spacing-2xl: var(--space-12);

    /* ===== BORDER RADIUS SYSTEM ===== */
    --radius-none: 0;
    --radius-sm: 0.375rem;   /* 6px */
    --radius-md: 0.5rem;     /* 8px */
    --radius-lg: 0.75rem;    /* 12px */
    --radius-xl: 1rem;       /* 16px */
    --radius-2xl: 1.5rem;    /* 24px */
    --radius-full: 9999px;

    /* ===== TYPOGRAPHY SYSTEM ===== */
    /* Font Families */
    --font-family-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-family-mono: 'JetBrains Mono', 'Fira Code', Consolas, monospace;

    /* Font Sizes */
    --text-xs: 0.75rem;      /* 12px */
    --text-sm: 0.875rem;     /* 14px */
    --text-base: 1rem;       /* 16px */
    --text-lg: 1.125rem;     /* 18px */
    --text-xl: 1.25rem;      /* 20px */
    --text-2xl: 1.5rem;      /* 24px */
    --text-3xl: 1.875rem;    /* 30px */
    --text-4xl: 2.25rem;     /* 36px */
    --text-5xl: 3rem;        /* 48px */

    /* Font Weights */
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;

    /* Line Heights */
    --leading-none: 1;
    --leading-tight: 1.25;
    --leading-snug: 1.375;
    --leading-normal: 1.5;
    --leading-relaxed: 1.625;
    --leading-loose: 2;

    /* ===== SHADOW SYSTEM ===== */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

    /* Brand Shadows */
    --shadow-brand: 0 4px 12px rgba(220, 38, 38, 0.15);
    --shadow-brand-lg: 0 10px 25px rgba(220, 38, 38, 0.2);

    /* ===== TRANSITION SYSTEM ===== */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.2s ease;
    --transition-slow: 0.3s ease;
    --transition-all: all var(--transition-normal);

    /* ===== Z-INDEX SYSTEM ===== */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
    --z-toast: 1080;

    /* ===== LAYOUT DIMENSIONS ===== */
    --sidebar-width: 200px;
    --topbar-height: 5rem;
    --footer-height: 3rem;
    --content-max-width: 1200px;

    /* ===== BREAKPOINTS (for reference in SCSS) ===== */
    /* These are used in SCSS mixins, not CSS custom properties */
    /* --breakpoint-sm: 640px; */
    /* --breakpoint-md: 768px; */
    /* --breakpoint-lg: 1024px; */
    /* --breakpoint-xl: 1280px; */
    /* --breakpoint-2xl: 1536px; */
}

/* ===== SCSS BREAKPOINT VARIABLES ===== */
$breakpoint-sm: 640px;
$breakpoint-md: 768px;
$breakpoint-lg: 1024px;
$breakpoint-xl: 1280px;
$breakpoint-2xl: 1536px;

/* ===== SCSS UTILITY VARIABLES ===== */
$border-radius: 12px;
$transition-duration: 0.2s;
$scale: 14px; // Base font size
