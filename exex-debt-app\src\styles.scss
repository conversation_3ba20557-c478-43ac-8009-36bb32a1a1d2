/* ===== GLOBAL STYLES - EXEX DEBT MANAGEMENT SYSTEM ===== */
/* Main stylesheet for Nisshin Vietnam Debt Management Application */

/* ===== SCSS VARIABLES ===== */
$gutter: 1rem; // PrimeFlex grid system

/* ===== EXTERNAL DEPENDENCIES ===== */
@import '../node_modules/primeng/resources/primeng.min.css';
@import '../node_modules/primeflex/primeflex.scss';
@import '../node_modules/primeicons/primeicons.css';

/* ===== DESIGN SYSTEM IMPORTS ===== */
@import 'assets/styles/index';
@import 'assets/layout/styles/layout/layout.scss';

/* ===== COMPONENT STYLING ===== */
/* Custom component styles and PrimeNG overrides */

/* ===== ENHANCED CARD STYLING ===== */
.card {
    @include card-interactive;
}

/* ===== CUSTOM COMPONENTS ===== */
/* Custom group button for table actions */
.custom-group-button-edit {
    .p-button.p-button-sm {
        padding: var(--space-2) var(--space-3);
        border-radius: var(--radius-sm);
        font-weight: var(--font-weight-medium);
        transition: var(--transition-all);

        &:hover {
            @include hover-lift(1px);
        }
    }
}

/* Custom avatar with brand styling */
.custom-avatar {
    .p-avatar {
        background: linear-gradient(135deg, var(--brand-primary), var(--brand-secondary));
        color: white;
        border: 2px solid white;
        @include card-shadow('md');
        transition: var(--transition-all);

        &:hover {
            transform: scale(1.05);
            @include card-shadow('lg');
        }
    }
}

/* ===== PRIMENG COMPONENT ENHANCEMENTS ===== */

/* Enhanced Table Styling */
.p-datatable {
    border-radius: var(--radius-lg);
    overflow: hidden;
    @include card-shadow('sm');
    border: 1px solid var(--border-color-enhanced);

    .p-datatable-header {
        border-bottom: 2px solid var(--brand-primary);
        padding: var(--spacing-lg);

        h5 {
            color: var(--brand-primary);
            font-weight: var(--font-weight-semibold);
            margin: 0;
        }
    }

    .p-datatable-thead > tr > th {
        background: var(--surface-hover-enhanced);
        color: var(--text-primary-enhanced);
        font-weight: var(--font-weight-semibold);
        padding: var(--spacing-md) var(--spacing-lg);
        border-bottom: 2px solid var(--border-color-enhanced);
    }

    .p-datatable-tbody > tr {
        transition: var(--transition-all);

        &:hover {
            background: var(--surface-hover-enhanced);
            transform: translateX(2px);
        }

        > td {
            padding: var(--spacing-md) var(--spacing-lg);
            border-bottom: 1px solid var(--border-color-enhanced);
        }
    }
}

/* Enhanced Button Styling */
.p-button {
    border-radius: var(--radius-md);
    font-weight: var(--font-weight-medium);
    transition: var(--transition-all);

    &.p-button-danger {
        background: linear-gradient(135deg, var(--brand-primary), var(--brand-secondary));
        border-color: var(--brand-primary);

        &:hover {
            background: linear-gradient(135deg, var(--brand-secondary), var(--brand-tertiary));
            @include hover-lift(1px);
            @include card-shadow('md');
        }
    }

    &.p-button-outlined {
        border: 2px solid var(--brand-primary);
        color: var(--brand-primary);

        &:hover {
            background: var(--brand-primary);
            color: white;
            @include hover-lift(1px);
        }
    }
}

/* Enhanced Dialog Styling */
.p-dialog {
    border-radius: var(--radius-xl);
    @include card-shadow('xl');
    border: none;

    .p-dialog-header {
        background: linear-gradient(135deg, var(--brand-primary), var(--brand-secondary));
        color: white;
        border-radius: var(--radius-xl) var(--radius-xl) 0 0;
        padding: var(--spacing-lg);

        .p-dialog-title {
            font-weight: var(--font-weight-semibold);
        }

        .p-dialog-header-icon {
            color: white;

            &:hover {
                background: rgba(255, 255, 255, 0.1);
            }
        }
    }

    .p-dialog-content {
        padding: var(--spacing-xl);
    }
}

/* Enhanced Input Styling */
.p-inputtext, .p-dropdown, .p-calendar {
    @include input-base;
}

/* Enhanced Toast Styling */
.p-toast {
    .p-toast-message {
        border-radius: var(--radius-lg);
        @include card-shadow('lg');
        border: none;

        &.p-toast-message-success {
            background: linear-gradient(135deg, var(--color-success), var(--color-success-dark));
        }

        &.p-toast-message-error {
            background: linear-gradient(135deg, var(--brand-primary), var(--brand-secondary));
        }

        &.p-toast-message-warn {
            background: linear-gradient(135deg, var(--color-warning), var(--color-warning-dark));
        }
    }
}

/* ===== RESPONSIVE DESIGN ENHANCEMENTS ===== */
@include tablet-only {
    .p-datatable {
        .p-datatable-header {
            padding: var(--spacing-md);

            h5 {
                font-size: var(--text-lg);
            }
        }

        .p-datatable-thead > tr > th,
        .p-datatable-tbody > tr > td {
            padding: var(--spacing-sm) var(--spacing-md);
            font-size: var(--text-sm);
        }
    }

    .p-dialog {
        margin: var(--spacing-md);
        width: calc(100% - 2rem) !important;

        .p-dialog-content {
            padding: var(--spacing-lg);
        }
    }

    .custom-group-button-edit {
        .p-button.p-button-sm {
            padding: var(--space-1) var(--space-2);
            font-size: var(--text-xs);
        }
    }
}

@include desktop-up {
    .p-datatable {
        .p-datatable-tbody > tr:hover {
            transform: translateX(4px);
        }
    }

    .card:hover {
        transform: translateY(-2px);
    }
}

/* ===== LEGACY UTILITY CLASSES ===== */
/* Brand-specific utilities for backward compatibility */
.text-nisshin-red {
    color: var(--brand-primary) !important;
}

.bg-nisshin-red {
    background: var(--brand-primary) !important;
}

.bg-nisshin-gradient {
    background: linear-gradient(135deg, var(--brand-primary), var(--brand-secondary)) !important;
}

.border-nisshin-red {
    border-color: var(--brand-primary) !important;
}

.shadow-enhanced {
    @include card-shadow('md');
}

.rounded-enhanced {
    border-radius: var(--radius-lg) !important;
}

.transition-smooth {
    transition: var(--transition-all) !important;
}

/* ===== ENHANCED ANIMATIONS ===== */
.animate-fade-in-up {
    @include fade-in();
}

.animate-slide-in-right {
    @include slide-up();
}

/* ===== CURVED DIVIDERS (BRIDGE STYLE) ===== */
.divider-curved {
    position: relative;
    height: 2px;
    background: linear-gradient(90deg,
        transparent 0%,
        var(--brand-primary) 20%,
        var(--brand-accent) 50%,
        var(--brand-primary) 80%,
        transparent 100%);
    border-radius: var(--radius-full);
    margin: var(--spacing-lg) 0;

    &::before {
        content: '';
        position: absolute;
        top: -1px;
        left: 10%;
        right: 10%;
        height: 4px;
        background: linear-gradient(90deg,
            transparent 0%,
            rgba(220, 38, 38, 0.3) 50%,
            transparent 100%);
        border-radius: var(--radius-full);
        filter: blur(1px);
    }
}

.divider-curved-small {
    position: relative;
    height: 1px;
    background: linear-gradient(90deg,
        transparent 0%,
        var(--brand-primary) 30%,
        var(--brand-primary) 70%,
        transparent 100%);
    border-radius: var(--radius-full);
    margin: var(--spacing-md) 0;
    width: 60%;
    margin-left: auto;
    margin-right: auto;
}

/* Section dividers with subtle curves */
.section-divider {
    position: relative;
    margin: var(--spacing-2xl) 0;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg,
            transparent 0%,
            var(--border-color-enhanced) 20%,
            var(--brand-primary) 50%,
            var(--border-color-enhanced) 80%,
            transparent 100%);
        border-radius: var(--radius-full);
    }

    &::after {
        content: '';
        position: absolute;
        top: -0.5px;
        left: 25%;
        right: 25%;
        height: 2px;
        background: linear-gradient(90deg,
            transparent 0%,
            rgba(220, 38, 38, 0.2) 50%,
            transparent 100%);
        border-radius: var(--radius-full);
        filter: blur(0.5px);
    }
}

/* ===== ENHANCED FORM STYLING ===== */
.field {
    @include form-group;

    .p-error {
        color: var(--brand-primary);
    }
}

/* ===== LOADING STATES ===== */
.loading-shimmer {
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

.layout-theme-light .loading-shimmer {
    background: linear-gradient(90deg,
        var(--gray-100) 25%,
        var(--gray-200) 50%,
        var(--gray-100) 75%);
}

.layout-theme-dark .loading-shimmer {
    background: linear-gradient(90deg,
        var(--gray-700) 25%,
        var(--gray-600) 50%,
        var(--gray-700) 75%);
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* ===== THEME-SPECIFIC TABLE STYLING ===== */
.layout-theme-light .p-datatable .p-datatable-header {
    background: linear-gradient(135deg, var(--brand-light), #ffffff);
}

.layout-theme-dark .p-datatable .p-datatable-header {
    background: linear-gradient(135deg, rgba(220, 38, 38, 0.1), var(--surface-card));
}
