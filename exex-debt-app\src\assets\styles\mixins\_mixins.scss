/* ===== SCSS MIXINS - REUSABLE STYLING PATTERNS ===== */
/* Centralized mixins for consistent and maintainable styling */

/* ===== RESPONSIVE BREAKPOINT MIXINS ===== */
@mixin mobile-only {
    @media (max-width: #{$breakpoint-sm - 1px}) {
        @content;
    }
}

@mixin tablet-up {
    @media (min-width: #{$breakpoint-sm}) {
        @content;
    }
}

@mixin tablet-only {
    @media (min-width: #{$breakpoint-sm}) and (max-width: #{$breakpoint-lg - 1px}) {
        @content;
    }
}

@mixin desktop-up {
    @media (min-width: #{$breakpoint-lg}) {
        @content;
    }
}

@mixin desktop-only {
    @media (min-width: #{$breakpoint-lg}) and (max-width: #{$breakpoint-xl - 1px}) {
        @content;
    }
}

@mixin large-desktop-up {
    @media (min-width: #{$breakpoint-xl}) {
        @content;
    }
}

/* Custom breakpoint mixin */
@mixin respond-to($breakpoint) {
    @media (min-width: #{$breakpoint}) {
        @content;
    }
}

/* ===== LAYOUT MIXINS ===== */
@mixin flex-center {
    display: flex;
    align-items: center;
    justify-content: center;
}

@mixin flex-between {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

@mixin flex-column-center {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

@mixin absolute-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

@mixin full-size {
    width: 100%;
    height: 100%;
}

/* ===== VISUAL EFFECT MIXINS ===== */
@mixin card-shadow($level: 'md') {
    @if $level == 'sm' {
        box-shadow: var(--shadow-sm);
    } @else if $level == 'md' {
        box-shadow: var(--shadow-md);
    } @else if $level == 'lg' {
        box-shadow: var(--shadow-lg);
    } @else if $level == 'xl' {
        box-shadow: var(--shadow-xl);
    }
}

@mixin brand-shadow($level: 'md') {
    @if $level == 'sm' {
        box-shadow: var(--shadow-brand);
    } @else if $level == 'lg' {
        box-shadow: var(--shadow-brand-lg);
    } @else {
        box-shadow: var(--shadow-brand);
    }
}

@mixin hover-lift($distance: 2px) {
    transition: var(--transition-all);
    
    &:hover {
        transform: translateY(-#{$distance});
        @include card-shadow('lg');
    }
}

@mixin focus-ring($color: var(--focus-ring-color)) {
    &:focus {
        outline: none;
        box-shadow: 0 0 0 3px #{$color};
    }
}

/* ===== BUTTON MIXINS ===== */
@mixin button-base {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-4);
    border: none;
    border-radius: var(--radius-md);
    font-weight: var(--font-weight-medium);
    font-size: var(--text-sm);
    line-height: var(--leading-tight);
    cursor: pointer;
    transition: var(--transition-all);
    text-decoration: none;
    
    &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        pointer-events: none;
    }
}

@mixin button-primary {
    @include button-base;
    background: linear-gradient(135deg, var(--brand-primary), var(--brand-secondary));
    color: white;
    
    &:hover:not(:disabled) {
        background: linear-gradient(135deg, var(--brand-secondary), var(--brand-tertiary));
        transform: translateY(-1px);
        @include brand-shadow();
    }
    
    &:active:not(:disabled) {
        transform: translateY(0);
    }
}

@mixin button-secondary {
    @include button-base;
    background: transparent;
    color: var(--brand-primary);
    border: 2px solid var(--brand-primary);
    
    &:hover:not(:disabled) {
        background: var(--brand-primary);
        color: white;
        transform: translateY(-1px);
    }
}

@mixin button-ghost {
    @include button-base;
    background: transparent;
    color: var(--text-color);
    
    &:hover:not(:disabled) {
        background: var(--surface-hover);
        transform: translateY(-1px);
    }
}

/* ===== FORM MIXINS ===== */
@mixin input-base {
    width: 100%;
    padding: var(--space-3) var(--space-4);
    border: 2px solid var(--border-color-enhanced);
    border-radius: var(--radius-md);
    background: var(--surface-card);
    color: var(--text-color);
    font-size: var(--text-sm);
    transition: var(--transition-all);
    
    &:focus {
        outline: none;
        border-color: var(--brand-primary);
        box-shadow: 0 0 0 3px var(--focus-ring-color);
    }
    
    &:disabled {
        background: var(--surface-disabled);
        opacity: 0.6;
        cursor: not-allowed;
    }
    
    &::placeholder {
        color: var(--text-color-secondary);
    }
}

@mixin form-group {
    margin-bottom: var(--space-4);
    
    label {
        display: block;
        margin-bottom: var(--space-2);
        font-weight: var(--font-weight-medium);
        color: var(--text-color);
        font-size: var(--text-sm);
    }
    
    .error-message {
        margin-top: var(--space-1);
        font-size: var(--text-xs);
        color: var(--color-error);
    }
}

/* ===== CARD MIXINS ===== */
@mixin card-base {
    background: var(--surface-card-enhanced);
    border: 1px solid var(--border-color-enhanced);
    border-radius: var(--radius-lg);
    padding: var(--space-6);
    @include card-shadow();
    transition: var(--transition-all);
}

@mixin card-interactive {
    @include card-base;
    cursor: pointer;
    
    &:hover {
        @include hover-lift();
    }
}

/* ===== TEXT MIXINS ===== */
@mixin text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

@mixin text-clamp($lines: 2) {
    display: -webkit-box;
    -webkit-line-clamp: #{$lines};
    -webkit-box-orient: vertical;
    overflow: hidden;
}

@mixin heading-base {
    font-weight: var(--font-weight-semibold);
    line-height: var(--leading-tight);
    color: var(--text-primary-enhanced);
    margin: 0;
}

@mixin heading-1 {
    @include heading-base;
    font-size: var(--text-4xl);
}

@mixin heading-2 {
    @include heading-base;
    font-size: var(--text-3xl);
}

@mixin heading-3 {
    @include heading-base;
    font-size: var(--text-2xl);
}

@mixin heading-4 {
    @include heading-base;
    font-size: var(--text-xl);
}

/* ===== ANIMATION MIXINS ===== */
@mixin fade-in($duration: var(--transition-normal)) {
    animation: fadeIn #{$duration} ease-out;
}

@mixin slide-up($duration: var(--transition-normal)) {
    animation: slideUp #{$duration} ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== UTILITY MIXINS ===== */
@mixin visually-hidden {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

@mixin clearfix {
    &::after {
        content: '';
        display: table;
        clear: both;
    }
}
